<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教育题目数据展示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .filters {
            padding: 20px;
            background: #fff;
            border-bottom: 1px solid #e9ecef;
        }
        
        .filter-group {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        
        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        
        .filter-group select, .filter-group input {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .question-list {
            padding: 20px;
        }
        
        .question-item {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
            transition: box-shadow 0.3s ease;
        }
        
        .question-item:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .question-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .question-id {
            font-weight: bold;
            color: #667eea;
            font-size: 1.1em;
        }
        
        .question-meta {
            display: flex;
            gap: 15px;
        }
        
        .meta-tag {
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            color: #495057;
        }
        
        .grade-tag {
            background: #28a745;
            color: white;
        }
        
        .ability-tag {
            background: #17a2b8;
            color: white;
        }
        
        .board-tag {
            background: #ffc107;
            color: #212529;
        }
        
        .question-content {
            padding: 20px;
        }
        
        .question-notice {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-style: italic;
            color: #1976d2;
        }
        
        .question-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #212529;
            line-height: 1.5;
        }
        
        .question-media {
            margin: 15px 0;
        }
        
        .question-media img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .question-media audio {
            width: 100%;
            margin: 10px 0;
        }
        
        .options {
            margin: 20px 0;
        }
        
        .option {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .option:hover {
            background: #e9ecef;
        }
        
        .option.correct {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
            font-weight: bold;
        }
        
        .option.correct::before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
        
        .question-details {
            background: #f8f9fa;
            padding: 15px;
            border-top: 1px solid #e9ecef;
            font-size: 0.9em;
            color: #6c757d;
        }
        
        .detail-row {
            display: flex;
            margin-bottom: 5px;
        }
        
        .detail-label {
            font-weight: bold;
            width: 120px;
            color: #495057;
        }
        
        .search-box {
            width: 300px;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .no-results {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-size: 1.2em;
        }
        
        @media (max-width: 768px) {
            .stats {
                flex-direction: column;
                gap: 15px;
            }
            
            .question-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .question-meta {
                flex-wrap: wrap;
            }
            
            .filter-group {
                display: block;
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 教育题目数据展示</h1>
            <p>结构化展示JSON题目数据</p>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalQuestions">0</div>
                <div class="stat-label">总题目数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="listeningQuestions">0</div>
                <div class="stat-label">听力题目</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="readingQuestions">0</div>
                <div class="stat-label">阅读题目</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="avgTitleLength">0</div>
                <div class="stat-label">平均题目长度</div>
            </div>
        </div>
        
        <div class="filters">
            <div class="filter-group">
                <label for="searchInput">搜索题目:</label>
                <input type="text" id="searchInput" class="search-box" placeholder="输入关键词搜索...">
            </div>
            
            <div class="filter-group">
                <label for="gradeFilter">年级筛选:</label>
                <select id="gradeFilter">
                    <option value="">所有年级</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="abilityFilter">能力类型:</label>
                <select id="abilityFilter">
                    <option value="">所有类型</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="boardFilter">知识板块:</label>
                <select id="boardFilter">
                    <option value="">所有板块</option>
                </select>
            </div>
        </div>
        
        <div class="question-list" id="questionList">
            <!-- 题目将通过JavaScript动态加载 -->
        </div>
    </div>

    <script>
        let questionsData = [];
        let filteredQuestions = [];

        // 嵌入的JSON数据 - 从题.json文件读取
        const embeddedQuestionsData = [
  {
    "level_tag_id": "22",
    "grade": "3",
    "grade_correct": "0",
    "notice": "Read and choose.",
    "title": "Amy loves music. She can sing many English songs. She likes Chinese music, too. She can _____ very well. She is also good at making things with her own hands. She can _____ very quickly.",
    "title_len": "186",
    "content": "{}",
    "section": "[\"dance; <br>make a puppet\",\"play the <i>pipa<\/i>; make a puppet\",\"do kung fu; <br>draw cartoons\",\"play the <i>pipa<\/i>; draw cartoons\"]",
    "section_count": "4",
    "section_maxlen": "35",
    "answer": "play the <i>pipa<\/i>; make a puppet",
    "answer_key": "{\"analysis_cn\":\"\",\"analysis_en\":\"\"}",
    "type": "11204",
    "tag": "",
    "source": "add",
    "sid": "0",
    "adapter": "1",
    "rev_order": "1",
    "ability": "reading",
    "ability_tag_uuid": "",
    "topic_uuid": "4c42067571d985b985d2ce11bf252073",
    "knowledge_uuid": "d65559ff5f20e4be113d02199601542b",
    "points_uuid": "2a8a99c09f2f558d502fb6864c45179a",
    "points_attach": "",
    "created_at": "1599735739501",
    "updated_at": "1599735739501",
    "disable": "0",
    "del_flag": "0",
    "board": "词汇掌握",
    "training": "阅读能力",
    "id": "87",
    "uuid": "186121d0-f355-11ea-9443-c10bc8ae4109",
    "level_id": "544403",
    "unit_id": "661732",
    "question_core_id": "252119"
  },
  {
    "id": "51",
    "uuid": "02bfe360-e69f-11ea-b270-575d3a483274",
    "level_id": "796041",
    "unit_id": "808751",
    "grade": "1",
    "grade_correct": "0",
    "notice": "Listen to the audio and choose the correct picture. ",
    "title": "听录音，选择正确的图片。",
    "title_len": "12",
    "content": "{\"audio\":\"http://s.kg.51talk.com/7e9b2031-d56a-4362-86d9-82ca8485d646.mp3\"}",
    "section": "[\"http://s.kg.51talk.com/3fbd0f0a-faff-4d48-b173-692aec734b9f.jpg\",\"http://s.kg.51talk.com/e82ae58e-830c-4090-9643-24edbfc7934c.jpg\",\"http://s.kg.51talk.com/5f1cbc30-1b75-4156-aeeb-0985b041a850.jpg\",\"http://s.kg.51talk.com/f9909ddd-aa23-4257-9fa2-b70d6b3c1f5c.jpg\"]",
    "section_count": "4",
    "section_maxlen": "63",
    "answer": "http://s.kg.51talk.com/e82ae58e-830c-4090-9643-24edbfc7934c.jpg",
    "answer_key": "{\"analysis_cn\":\"\",\"analysis_en\":\"\"}",
    "type": "1102",
    "tag": "",
    "source": "add",
    "sid": "0",
    "adapter": "1",
    "rev_order": "1",
    "ability": "listening",
    "ability_tag_uuid": "1d5a2a3aca6bf22d21011d810364992e",
    "knowledge_uuid": "d65559ff5f20e4be113d02199601542b",
    "topic_uuid": "6c1ffb6d5c4635ba7abe8e7b51a49cff",
    "points_uuid": "ed3c9aa7690d11a870ddf5e4e04e5e98",
    "points_attach": "",
    "created_at": "1597245383200",
    "updated_at": "1625124867448",
    "disable": "0",
    "del_flag": "0",
    "question_core_id": "251579",
    "board": "词汇掌握",
    "training": "听力理解",
    "level_tag_id": "4",
    "ability_label_des_id": "84",
    "updated_by": "51aitongji",
    "ability_label_id": "1",
    "knowledge_des_id": "49"
  },
  {
    "id": "27",
    "uuid": "dc533700-e698-11ea-81a7-832e26b69caf",
    "level_id": "772051",
    "unit_id": "772131",
    "grade": "2",
    "grade_correct": "0",
    "notice": "Listen and choose the correct picture.",
    "title": "听录音，选择正确的图片。",
    "title_len": "12",
    "content": "{\"audio\":\"http://s.kg.51talk.com/f4d94abd-873f-4f88-a32c-5a2cb7aacba9.mp3\"}",
    "section": "[\"http://s.kg.51talk.com/6a108454-84db-4b7c-b836-2da483e1c542.jpg\",\"http://s.kg.51talk.com/79683930-e62a-4783-8bdf-891b83f17a78.jpg\",\"http://s.kg.51talk.com/28caf497-f2a4-4a10-9ecd-5f7eb59df3e4.png\",\"http://s.kg.51talk.com/2826e6e1-6bbd-4e44-a6b7-ff2dd6558577.jpg\"]",
    "section_count": "4",
    "section_maxlen": "63",
    "answer": "http://s.kg.51talk.com/28caf497-f2a4-4a10-9ecd-5f7eb59df3e4.png",
    "answer_key": "{\"analysis_cn\":\"\",\"analysis_en\":\"\"}",
    "type": "1102",
    "tag": "",
    "source": "add",
    "sid": "0",
    "adapter": "1",
    "rev_order": "1",
    "ability": "listening",
    "ability_tag_uuid": "199eda18c1c4dfd61f137c4a44a670fd",
    "knowledge_uuid": "d65559ff5f20e4be113d02199601542b",
    "topic_uuid": "bfacbfb1fc250688534038c796526f7a",
    "points_uuid": "226cf6bb5487dcf2d620583d5383ddfa",
    "points_attach": "",
    "created_at": "1597046552633",
    "updated_at": "1625121605892",
    "disable": "0",
    "del_flag": "0",
    "question_core_id": "251417",
    "board": "词汇掌握",
    "training": "听力理解",
    "level_tag_id": "2",
    "ability_label_des_id": "102",
    "updated_by": "51aitongji",
    "ability_label_id": "11",
    "knowledge_des_id": "25"
  },
  {
    "id": "17",
    "uuid": "8de6f460-e5eb-11ea-81a7-832e26b69caf",
    "level_id": "769941",
    "unit_id": "793551",
    "grade": "2",
    "grade_correct": "0",
    "notice": "Choose the correct answer.",
    "title": "The fish live in the ___.\n",
    "title_len": "26",
    "content": "{}",
    "section": "[\"land\",\" water \",\"house\",\"sky\"]",
    "section_count": "4",
    "section_maxlen": "7",
    "answer": " water ",
    "answer_key": "{\"analysis_cn\":\"\",\"analysis_en\":\"\"}",
    "type": "1204",
    "tag": "",
    "source": "add",
    "sid": "0",
    "adapter": "1",
    "rev_order": "1",
    "ability": "reading",
    "ability_tag_uuid": "8b2c5f8377408dc43b041476710baaf1",
    "knowledge_uuid": "b725803ade936e8ee05336dbbbf34ab7",
    "topic_uuid": "95cc7d14214ccb2508699cc8e971791e",
    "points_uuid": "b07d4ee6ad1cccb2c76c0a8bc6c67fc2",
    "points_attach": "",
    "created_at": "1596793998534",
    "updated_at": "1625119717373",
    "disable": "0",
    "del_flag": "0",
    "question_core_id": "251381",
    "board": "综合理解",
    "training": "阅读能力",
    "level_tag_id": "1",
    "updated_by": "51aitongji",
    "ability_label_des_id": "113",
    "ability_label_id": "17",
    "knowledge_des_id": "16"
  },
  {
    "id": "3",
    "uuid": "c0fa80f0-e5e7-11ea-b270-575d3a483274",
    "level_id": "773011",
    "unit_id": "793011",
    "grade": "1",
    "grade_correct": "0",
    "notice": "Choose the correct picture.",
    "title": "Good afternoon!",
    "title_len": "15",
    "content": "{}",
    "section": "[\"http://s.kg.51talk.com/448da6a3-11e8-4916-884a-58726eef2491.jpg\",\"http://s.kg.51talk.com/07015bb3-de02-49b6-9e33-7b99a58aedd7.jpg\",\"http://s.kg.51talk.com/b4c5810b-3727-4a66-8cb0-731872e96c08.jpg\",\"http://s.kg.51talk.com/037f0165-1805-4375-88c4-d2098443f152.jpg\"]",
    "section_count": "4",
    "section_maxlen": "63",
    "answer": "http://s.kg.51talk.com/07015bb3-de02-49b6-9e33-7b99a58aedd7.jpg",
    "answer_key": "{\"analysis_cn\":\"\",\"analysis_en\":\"\"}",
    "type": "1104",
    "tag": "",
    "source": "add",
    "sid": "0",
    "adapter": "1",
    "rev_order": "1",
    "ability": "reading",
    "ability_tag_uuid": "8b2c5f8377408dc43b041476710baaf1",
    "knowledge_uuid": "d65559ff5f20e4be113d02199601542b",
    "topic_uuid": "fa19e70ab76b6dd9316c2500d9221d1e",
    "points_uuid": "33022fd297ba706704f200583d1647be",
    "points_attach": "",
    "created_at": "1596104146747",
    "updated_at": "1625118491382",
    "disable": "0",
    "del_flag": "0",
    "question_core_id": "251306",
    "board": "词汇掌握",
    "training": "阅读能力",
    "level_tag_id": "0",
    "ability_label_des_id": "87",
    "updated_by": "51aitongji",
    "ability_label_id": "2",
    "knowledge_des_id": "1"
  }
]; // 这里只显示前5个题目作为示例

        // 加载数据
        function loadQuestionsData() {
            try {
                if (embeddedQuestionsData) {
                    questionsData = embeddedQuestionsData;
                } else {
                    // 如果没有嵌入数据，显示错误信息
                    document.getElementById('questionList').innerHTML =
                        '<div class="no-results">❌ 数据未正确嵌入，请检查HTML文件</div>';
                    return;
                }

                filteredQuestions = [...questionsData];

                initializeFilters();
                updateStats();
                renderQuestions();
            } catch (error) {
                console.error('加载数据失败:', error);
                document.getElementById('questionList').innerHTML =
                    '<div class="no-results">❌ 数据加载失败: ' + error.message + '</div>';
            }
        }

        // 初始化筛选器选项
        function initializeFilters() {
            const grades = [...new Set(questionsData.map(q => q.grade))].sort();
            const abilities = [...new Set(questionsData.map(q => q.ability))].sort();
            const boards = [...new Set(questionsData.map(q => q.board))].sort();

            populateSelect('gradeFilter', grades);
            populateSelect('abilityFilter', abilities);
            populateSelect('boardFilter', boards);
        }

        function populateSelect(selectId, options) {
            const select = document.getElementById(selectId);
            options.forEach(option => {
                if (option) {
                    const optionElement = document.createElement('option');
                    optionElement.value = option;
                    optionElement.textContent = option;
                    select.appendChild(optionElement);
                }
            });
        }

        // 更新统计信息
        function updateStats() {
            const total = questionsData.length;
            const listening = questionsData.filter(q => q.ability === 'listening').length;
            const reading = questionsData.filter(q => q.ability === 'reading').length;
            const avgLength = Math.round(questionsData.reduce((sum, q) => sum + parseInt(q.title_len || 0), 0) / total);

            document.getElementById('totalQuestions').textContent = total;
            document.getElementById('listeningQuestions').textContent = listening;
            document.getElementById('readingQuestions').textContent = reading;
            document.getElementById('avgTitleLength').textContent = avgLength;
        }

        // 渲染题目列表
        function renderQuestions() {
            const questionList = document.getElementById('questionList');

            if (filteredQuestions.length === 0) {
                questionList.innerHTML = '<div class="no-results">🔍 没有找到匹配的题目</div>';
                return;
            }

            questionList.innerHTML = filteredQuestions.map(question => createQuestionHTML(question)).join('');
        }

        // 创建单个题目的HTML
        function createQuestionHTML(question) {
            const content = parseContent(question.content);
            const sections = parseSection(question.section);
            const answer = question.answer;

            return `
                <div class="question-item">
                    <div class="question-header">
                        <div class="question-id">题目 #${question.id}</div>
                        <div class="question-meta">
                            <span class="meta-tag grade-tag">年级 ${question.grade}</span>
                            <span class="meta-tag ability-tag">${getAbilityText(question.ability)}</span>
                            <span class="meta-tag board-tag">${question.board}</span>
                        </div>
                    </div>

                    <div class="question-content">
                        ${question.notice ? `<div class="question-notice">📋 ${question.notice}</div>` : ''}

                        <div class="question-title">${formatTitle(question.title)}</div>

                        ${createMediaHTML(content)}

                        ${sections.length > 0 ? createOptionsHTML(sections, answer) : ''}
                    </div>

                    <div class="question-details">
                        <div class="detail-row">
                            <span class="detail-label">题目类型:</span>
                            <span>${question.type}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">训练类型:</span>
                            <span>${question.training}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">创建时间:</span>
                            <span>${formatDate(question.created_at)}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">更新时间:</span>
                            <span>${formatDate(question.updated_at)}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 解析content字段
        function parseContent(contentStr) {
            try {
                return JSON.parse(contentStr || '{}');
            } catch {
                return {};
            }
        }

        // 解析section字段
        function parseSection(sectionStr) {
            try {
                return JSON.parse(sectionStr || '[]');
            } catch {
                return [];
            }
        }

        // 获取能力类型的中文文本
        function getAbilityText(ability) {
            const abilityMap = {
                'listening': '听力',
                'reading': '阅读',
                'speaking': '口语',
                'writing': '写作'
            };
            return abilityMap[ability] || ability;
        }

        // 格式化标题
        function formatTitle(title) {
            return title.replace(/\n/g, '<br>').replace(/<i>(.*?)<\/i>/g, '<em>$1</em>');
        }

        // 创建媒体内容HTML
        function createMediaHTML(content) {
            let html = '';

            if (content.audio) {
                html += `
                    <div class="question-media">
                        <audio controls>
                            <source src="${content.audio}" type="audio/mpeg">
                            您的浏览器不支持音频播放。
                        </audio>
                    </div>
                `;
            }

            if (content.picture) {
                html += `
                    <div class="question-media">
                        <img src="${content.picture}" alt="题目图片" onerror="this.style.display='none'">
                    </div>
                `;
            }

            return html;
        }

        // 创建选项HTML
        function createOptionsHTML(sections, answer) {
            if (sections.length === 0) return '';

            return `
                <div class="options">
                    ${sections.map(option => {
                        const isCorrect = option === answer;
                        const isImage = option.startsWith('http') && (option.includes('.jpg') || option.includes('.png'));

                        if (isImage) {
                            return `
                                <div class="option ${isCorrect ? 'correct' : ''}">
                                    <img src="${option}" alt="选项图片" style="max-width: 200px; height: auto;" onerror="this.style.display='none'">
                                </div>
                            `;
                        } else {
                            return `
                                <div class="option ${isCorrect ? 'correct' : ''}">${formatTitle(option)}</div>
                            `;
                        }
                    }).join('')}
                </div>
            `;
        }

        // 格式化日期
        function formatDate(timestamp) {
            if (!timestamp) return '未知';
            const date = new Date(parseInt(timestamp));
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN');
        }

        // 筛选功能
        function filterQuestions() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const gradeFilter = document.getElementById('gradeFilter').value;
            const abilityFilter = document.getElementById('abilityFilter').value;
            const boardFilter = document.getElementById('boardFilter').value;

            filteredQuestions = questionsData.filter(question => {
                const matchesSearch = !searchTerm ||
                    question.title.toLowerCase().includes(searchTerm) ||
                    question.notice.toLowerCase().includes(searchTerm) ||
                    question.board.toLowerCase().includes(searchTerm);

                const matchesGrade = !gradeFilter || question.grade === gradeFilter;
                const matchesAbility = !abilityFilter || question.ability === abilityFilter;
                const matchesBoard = !boardFilter || question.board === boardFilter;

                return matchesSearch && matchesGrade && matchesAbility && matchesBoard;
            });

            renderQuestions();
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            loadQuestionsData();

            // 添加筛选事件监听器
            document.getElementById('searchInput').addEventListener('input', filterQuestions);
            document.getElementById('gradeFilter').addEventListener('change', filterQuestions);
            document.getElementById('abilityFilter').addEventListener('change', filterQuestions);
            document.getElementById('boardFilter').addEventListener('change', filterQuestions);
        });
    </script>
</body>
</html>

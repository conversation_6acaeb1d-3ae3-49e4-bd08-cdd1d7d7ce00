<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试JSON加载</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .loading {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 JSON文件加载测试</h1>
        
        <div id="status" class="status loading">
            ⏳ 正在尝试加载题.json文件...
        </div>
        
        <div class="debug-info">
            <strong>调试信息:</strong><br>
            <div id="debugInfo">初始化中...</div>
        </div>
        
        <div class="stats" id="statsSection" style="display: none;">
            <div class="stat-item">
                <div class="stat-number" id="totalQuestions">0</div>
                <div class="stat-label">总题目数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="grade1Questions">0</div>
                <div class="stat-label">1年级题目</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="grade2Questions">0</div>
                <div class="stat-label">2年级题目</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="grade3Questions">0</div>
                <div class="stat-label">3年级题目</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="listeningQuestions">0</div>
                <div class="stat-label">听力题目</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="readingQuestions">0</div>
                <div class="stat-label">阅读题目</div>
            </div>
        </div>
        
        <div id="sampleQuestions" style="display: none;">
            <h3>前3个题目示例:</h3>
            <div id="questionSamples"></div>
        </div>
    </div>

    <script>
        let questionsData = [];
        
        function updateDebugInfo(message) {
            const debugDiv = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `<br>[${timestamp}] ${message}`;
        }
        
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        async function loadJsonFile() {
            try {
                updateDebugInfo('开始请求题.json文件...');
                updateStatus('🔄 正在请求题.json文件...', 'loading');
                
                // 尝试不同的文件名编码方式
                let response;
                const filenames = [
                    '题.json',
                    '%E9%A2%98.json',
                    encodeURIComponent('题.json')
                ];

                updateDebugInfo(`尝试的文件名: ${filenames.join(', ')}`);

                for (const filename of filenames) {
                    try {
                        updateDebugInfo(`尝试请求: ${filename}`);
                        response = await fetch(filename);
                        if (response.ok) {
                            updateDebugInfo(`成功请求: ${filename}`);
                            break;
                        } else {
                            updateDebugInfo(`请求失败 ${filename}: ${response.status}`);
                        }
                    } catch (err) {
                        updateDebugInfo(`请求异常 ${filename}: ${err.message}`);
                    }
                }
                updateDebugInfo(`HTTP响应状态: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                updateDebugInfo('开始解析JSON数据...');
                const jsonData = await response.json();
                
                updateDebugInfo(`JSON解析成功，数据类型: ${typeof jsonData}`);
                updateDebugInfo(`数据是数组: ${Array.isArray(jsonData)}`);
                
                if (Array.isArray(jsonData)) {
                    updateDebugInfo(`数组长度: ${jsonData.length}`);
                    questionsData = jsonData;
                } else {
                    updateDebugInfo('数据不是数组，转换为数组');
                    questionsData = [jsonData];
                }
                
                updateStatus('✅ 题.json文件加载成功！', 'success');
                updateDebugInfo('开始更新统计信息...');
                
                updateStats();
                showSampleQuestions();
                
            } catch (error) {
                updateDebugInfo(`加载失败: ${error.message}`);
                updateStatus('❌ 加载失败: ' + error.message, 'error');
                console.error('详细错误信息:', error);
            }
        }
        
        function updateStats() {
            const total = questionsData.length;
            const grade1 = questionsData.filter(q => q.grade === '1').length;
            const grade2 = questionsData.filter(q => q.grade === '2').length;
            const grade3 = questionsData.filter(q => q.grade === '3').length;
            const listening = questionsData.filter(q => q.ability === 'listening').length;
            const reading = questionsData.filter(q => q.ability === 'reading').length;
            
            document.getElementById('totalQuestions').textContent = total;
            document.getElementById('grade1Questions').textContent = grade1;
            document.getElementById('grade2Questions').textContent = grade2;
            document.getElementById('grade3Questions').textContent = grade3;
            document.getElementById('listeningQuestions').textContent = listening;
            document.getElementById('readingQuestions').textContent = reading;
            
            document.getElementById('statsSection').style.display = 'grid';
            
            updateDebugInfo(`统计完成 - 总计:${total}, 1年级:${grade1}, 2年级:${grade2}, 3年级:${grade3}, 听力:${listening}, 阅读:${reading}`);
        }
        
        function showSampleQuestions() {
            const samples = questionsData.slice(0, 3);
            const samplesDiv = document.getElementById('questionSamples');
            
            samplesDiv.innerHTML = samples.map((q, index) => `
                <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;">
                    <strong>题目 ${index + 1} (ID: ${q.id})</strong><br>
                    年级: ${q.grade} | 能力: ${q.ability} | 板块: ${q.board}<br>
                    标题: ${q.title.substring(0, 100)}${q.title.length > 100 ? '...' : ''}
                </div>
            `).join('');
            
            document.getElementById('sampleQuestions').style.display = 'block';
            updateDebugInfo('示例题目显示完成');
        }
        
        // 页面加载完成后自动开始
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('页面DOM加载完成');
            updateDebugInfo(`当前URL: ${window.location.href}`);
            updateDebugInfo(`基础URL: ${window.location.origin}`);
            
            // 延迟一秒开始加载，确保页面完全准备好
            setTimeout(() => {
                updateDebugInfo('开始加载JSON文件...');
                loadJsonFile();
            }, 1000);
        });
    </script>
</body>
</html>
